import version from "../../../../startScene/version"
import PassengerMgr from "../../model/passenger/PassengerMgr"
import TrainModel from "../../model/train/TrainModel"
import AdModel from "../ad/AdModel"
import { wxHelper } from "../wx/WxHelper"
import { viewHelper } from "./ViewHelper"
import MoneyManager from "../../model/currency/CurrencyModel"
import ConditionObj from "../../model/common/ConditionObj"
import BagModel from "../../model/bag/BagModel"
import PlanetMgr from "../../model/planet/PlanetMgr"
import HeroModel from "../../model/hero/HeroModel"
import {
    CharacterCfg,
    Condition,
    InstanceLevelCfg,
    ItemCfg,
    JumpTipsCfg,
    PlanetMonsterCfg,
    TaskTarget,
    TypeIds
} from "../constant/DataType"
import EventType from "../event/EventType"
import BattleMgr from "../../model/battle/BattleMgr"
import JackpotModel from '../../model/jackpot/JackpotModel'
import { cfgHelper } from "./CfgHelper"
import { CURRENY_CFG, TIME_LANG, TIME_SHORT_LANG } from "../constant/Constant"
import GuideModel from "../../model/guide/GuideModel"
import { BuildAttr, ConditionType, ItemID, Platform, TrainBurstTaskType } from "../constant/Enums"
import SetModel from '../../model/setting/SetModel'
import UserModel from "../../model/common/UserModel"
import PlotModel from "../../model/common/PlotModel"
import WorldModel from "../../model/world/WorldModel"
import WeakGuideModel from "../../model/guide/WeakGuideModel"
import ShopModel from "../../model/shop/ShopModel"
import TaskModel from "../../model/task/TaskModel"
import ToolModel from "../../model/tool/ToolModel"
import NewModel from "../../model/new/NewModel"
import PassengerModel from "../../model/passenger/PassengerModel"
import NetworkModel from "../net/NetworkModel"
import JsbEvent from "../event/JsbEvent"
import MailModel from "../../model/mail/MailModel"
import AchieveModel from "../../model/achieve/AchieveModel"
import { jsbHelper } from "./JsbHelper"
import { localConfig } from "../LocalConfig"
import CharacterPlotModel from "../../model/common/CharacterPlotModel"
import { dropItemHelper } from "./DropItemHelper"
import HeartOutputModel from "../../model/train/common/HeartOutputModel"
import BlackHoleModel from "../../model/blackHole/BlackHoleModel"
import ChestModel from "../../model/chest/ChestModel"
import TowerModel from "../../model/tower/TowerModel"
import EquipModel from "../../model/equip/EquipModel"
import InstanceModel from "../../model/instance/InstanceModel"
import { BattleRoleType } from "../../model/battle/BattleEnum"
import WantedModel from "../../model/wanted/WantedModel"
import StoreMgr from "../../model/store/StoreMgr"
import PayModel from "../../model/train/common/PayModel"
import ResonanceModel from "../../model/resonance/ResonanceModel"
import TransportModel from "../../model/transport/TransportModel"
import FieldModel from "../../model/field/FieldModel"
import BattleRole from "../../model/battle/BattleRole"
import OreModel from "../../model/ore/OreModel"
import CollectModel from "../../model/collect/CollectModel"
import ArrestModel from "../../model/arrest/ArrestModel"
import SpaceStoneModel from "../../model/stone/SpaceStoneModel"
import DailyTaskModel from "../../model/daily_task/DailyTaskModel"
import ArchivesModel from "../../model/archives/ArchivesModel"
import PvpModel from "../../model/pvp/PvpModel"
import DeepExploreModel from "../../model/planet/DeepExploreModel"
import PlanetArchivesModel from "../../model/archives/PlanetArchivesModel"
import ProfileBranchMgr from "../../model/archives/ProfileBranchMgr"
import TrainDailyTaskModel, { TrainDailyTaskItem } from "../../model/trainDailyTask/TrainDailyTaskModel"
import BurstTaskModel from "../../model/planet/burstTask/BurstTaskModel"
import TrainActivityModel from "../../model/trainActivity/TrainActivityModel"
import TrainTechModel from "../../model/train/tech/TrainTechModel"

class GameHelper {

    public clickTouchId: number = -1

    private debugTime: number = 0;

    private timeoutIdMap: {} = {}
    private intervalIdMap: {} = {}

    public isEnterSecne: boolean = false //是否已经进入场景
    public isLogin: boolean = false //是否已经登录
    public isShow: boolean = true //是否在前台

    public autoClick: boolean = false
    public isTimeBack: boolean = false
    public isFakeGuide: boolean = false
    public get openGuide() { return localConfig.openGuide || this.isFakeGuide }

    private _resetTime: number = -1
    public get resetTime() {
        if (this._resetTime < 0) {
            this._resetTime = ut.stringToMs(cfgHelper.getMiscData("refreshTime"))
        }
        return this._resetTime
    }

    private _ad: AdModel = null
    private _train: TrainModel = null
    private _setting: SetModel = null
    private _passenger: PassengerMgr = null
    private _currency: MoneyManager = null;
    private _planet: PlanetMgr = null
    private _jackpot: JackpotModel = null
    private _hero: HeroModel = null
    private _new: NewModel = null
    private _bag: BagModel = null
    private _plot: PlotModel = null
    private _characterPlot: CharacterPlotModel = null
    private _battle: BattleMgr = null
    private _guide: GuideModel = null
    private _user: UserModel = null
    private _shop: ShopModel = null;// 暂时用来存放过审用的充值数据
    private _task: TaskModel = null
    private _tool: ToolModel = null
    private _world: WorldModel = null
    private _weakGuide: WeakGuideModel = null
    private _net: NetworkModel = null
    private _achieve: AchieveModel = null
    private _mail: MailModel = null
    private _heartOutput: HeartOutputModel = null
    private _blackHole: BlackHoleModel = null
    private _chest: ChestModel = null
    private _tower: TowerModel = null
    private _equip: EquipModel = null
    private _instance: InstanceModel = null
    private _wanted: WantedModel = null
    private _store: StoreMgr = null
    private _pay: PayModel = null
    private _resonance: ResonanceModel = null
    private _transport: TransportModel = null
    private _field: FieldModel = null
    private _ore: OreModel = null
    private _collect: CollectModel = null
    private _arrest: ArrestModel = null
    private _spaceStone: SpaceStoneModel = null
    private _dailyTask: DailyTaskModel = null
    private _archives: ArchivesModel = null
    private _pvp: PvpModel = null
    private _deepExplore: DeepExploreModel = null
    private _planetArchives: PlanetArchivesModel = null
    private _profileBranch: ProfileBranchMgr = null
    private _trainDailyTask: TrainDailyTaskModel<TrainDailyTaskItem> = null
    private _burstTask: BurstTaskModel = null
    private _trainActivity: TrainActivityModel = null
    private _trainTech: TrainTechModel = null

    public get ad() { return this._ad || (this._ad = mc.modelMgr.get('ad')) }
    public get train() { return this._train || (this._train = mc.modelMgr.get('train')) }
    public get passenger() { return this._passenger || (this._passenger = mc.modelMgr.get('passenger')) }
    public get currency() { return this._currency || (this._currency = mc.modelMgr.get('currency')) }
    public get planet() { return this._planet || (this._planet = mc.modelMgr.get('planet')) }
    public get jackpot() { return this._jackpot || (this._jackpot = mc.modelMgr.get('jackpot')) }
    public get hero() { return this._hero || (this._hero = mc.modelMgr.get('hero')) }
    public get new() { return this._new || (this._new = mc.modelMgr.get('new')) }
    public get bag() { return this._bag || (this._bag = mc.modelMgr.get('bag')) }
    public get plot() { return this._plot || (this._plot = mc.modelMgr.get('plot')) }
    public get characterPlot() { return this._characterPlot || (this._characterPlot = mc.modelMgr.get('characterPlot')) }
    public get battle() { return this._battle || (this._battle = mc.modelMgr.get('battle')) }
    public get guide() { return this._guide || (this._guide = mc.modelMgr.get('guide')) }
    public get setting() { return this._setting || (this._setting = mc.modelMgr.get('set')) }
    public get user() { return this._user || (this._user = mc.modelMgr.get('user')) }
    public get shop() { return this._shop || (this._shop = mc.modelMgr.get('Shop')) }
    public get task() { return this._task || (this._task = mc.modelMgr.get('task')) }
    public get tool() { return this._tool || (this._tool = mc.modelMgr.get('tool')) }
    public get world() { return this._world || (this._world = mc.modelMgr.get('world')) }
    public get weakGuide() { return this._weakGuide || (this._weakGuide = mc.modelMgr.get('weakGuide')) }
    public get net() { return this._net || (this._net = mc.modelMgr.get('net')) }
    public get achieve() { return this._achieve || (this._achieve = mc.modelMgr.get('achieve')) }
    public get mail() { return this._mail || (this._mail = mc.modelMgr.get('mail')) }
    public get heartOutput() { return this._heartOutput || (this._heartOutput = mc.modelMgr.get('heartOutput')) }
    public get blackHole() { return this._blackHole || (this._blackHole = mc.modelMgr.get('blackHole')) }
    public get chest() { return this._chest || (this._chest = mc.modelMgr.get('chest')) }
    public get tower() { return this._tower || (this._tower = mc.modelMgr.get('tower')) }
    public get equip() { return this._equip || (this._equip = mc.modelMgr.get('equip')) }
    public get instance() { return this._instance || (this._instance = mc.modelMgr.get('instance')) }
    public get wanted() { return this._wanted || (this._wanted = mc.modelMgr.get('wanted')) }
    public get store() { return this._store || (this._store = mc.modelMgr.get('store')) }
    public get pay() { return this._pay || (this._pay = mc.modelMgr.get('pay')) }
    public get resonance() { return this._resonance || (this._resonance = mc.modelMgr.get('resonance')) }
    public get transport() { return this._transport || (this._transport = mc.modelMgr.get('transport')) }
    public get field() { return this._field || (this._field = mc.modelMgr.get('field')) }
    public get ore() { return this._ore || (this._ore = mc.modelMgr.get('ore')) }
    public get collect() { return this._collect || (this._collect = mc.modelMgr.get('collect')) }
    public get arrest() { return this._arrest || (this._arrest = mc.modelMgr.get('arrest')) }
    public get spaceStone() { return this._spaceStone || (this._spaceStone = mc.modelMgr.get('spaceStone')) }
    public get dailyTask() { return this._dailyTask || (this._dailyTask = mc.modelMgr.get('dailyTask')) }
    public get archives() { return this._archives || (this._archives = mc.modelMgr.get('archives')) }
    public get pvp() { return this._pvp || (this._pvp = mc.modelMgr.get('pvp')) }
    public get deepExplore() { return this._deepExplore || (this._deepExplore = mc.modelMgr.get('deepExplore')) }
    public get planetArchives() { return this._planetArchives || (this._planetArchives = mc.modelMgr.get('planetArchives')) }
    public get profileBranch() { return this._profileBranch || (this._profileBranch = mc.modelMgr.get('profileBranch')) }
    public get trainDailyTask() { return this._trainDailyTask || (this._trainDailyTask = mc.modelMgr.get('trainDailyTask')) }
    public get burstTask() { return this._burstTask || (this._burstTask = mc.modelMgr.get('burstTask')) }
    public get trainActivity() { return this._trainActivity || (this._trainActivity = mc.modelMgr.get('trainActivity')) }
    public get trainTech() { return this._trainTech || (this._trainTech = mc.modelMgr.get('trainTech')) }

    public getVersion() { return version.VERSION }

    public now() {
        let now = Date.now()
        return now + this.debugTime
    }

    public setDebugTime(time: number) {
        this.debugTime = time || 0;
    }

    //获取平台
    public getPlatform() {
        if (ut.isMobile()) {
            if (ut.isAndroid()) {
                return Platform.ANDROID
            } else {
                return Platform.IOS
            }
        } else if (ut.isMiniGame()) {
            if (typeof qq != "undefined") {
                return Platform.QQ
            }
            return Platform.WX
        } else if (cc.sys.isBrowser) {
            return Platform.WEB
        }
        else {
            return Platform.UNKNOWN
        }
    }

    public unlockTrainFree(id?: number) {
        if (id == undefined) {
            let carriages = this.train.getCarriages();
            for (let i = 0; i < carriages.length; ++i) {
                for (let j = 0; j < carriages[i]['themes'].length; ++j) {
                    carriages[i]['themes'][j]['unlockFree'](true);
                }
            }
        } else {

        }
    }


    // 获取多语言参数
    public getLocaleParam(val: any) {
        if (typeof (val) === 'string') {
            if (val.includes(',')) {
                return val.split(',')
            } else if (val.includes('|')) {
                return val.split('|')
            }
        }
        return val
    }

    public gameRestart() {
        audioMgr.stopAll()
        audioMgr.releaseAll()
        wxHelper.destroyAllButton()

        if (ut.isMiniGame()) {
            wx.restartMiniProgram();
        }
        else if (cc.sys.isBrowser) {
            location.reload()
        }
        else {
            cc.game.restart()
        }
    }

    // 低内存警告 清理资源
    public cleanByMemoryWarning() {
        // 清理所有没有打开的UI
        eventCenter.emit(mc.Event.CLEAN_ALL_UNUSED)
        // 清理所有缓存的场景
        eventCenter.emit(mc.Event.CLEAN_CACHE_WIND)
    }

    // 根据权重随机
    randomByWeight(arr: any[], weightKey: string = "weight"): number {
        if (arr.length <= 0) {
            twlog.error("randomByWeight arr is empty")
            return -1
        }
        let getVal = (val) => {
            if (typeof val == 'number') return val
            return val[weightKey] || 0
        }
        let totalWeight = arr.reduce((sum, x) => sum + getVal(x), 0)
        if (totalWeight > 0) {
            let offset = Math.random() * totalWeight
            for (let i = 0; i < arr.length; i++) {
                offset -= getVal(arr[i])
                if (offset <= 0) {
                    return i
                }
            }
        }
        return ut.randomIndex(arr.length)
    }

    public setTimeout(handler: TimerHandler, timeout?: number, ...args: any[]): number {
        let id: any = setTimeout(() => {
            delete this.timeoutIdMap[id]
            if (typeof handler == 'function') {
                handler()
            }
        }, timeout, args)
        this.timeoutIdMap[id] = id
        return id
    }

    public clearTimeout(id: number) {
        delete this.timeoutIdMap[id]
        clearTimeout(id)
    }

    public setInterval(handler: TimerHandler, timeout?: number, ...args: any[]): number {
        let id = setInterval(handler, timeout, args)
        this.intervalIdMap[id] = id
        return id
    }

    public clearInterval(id: number) {
        delete this.intervalIdMap[id]
        clearInterval(id)
    }

    public toConditions(conds?: Condition[] | proto.ICondition[]) {
        if (!conds) return []
        return conds.map(c => c && this.toCondition(c)).filter(c => !!c)
    }
    public toCondition(cond: Condition | proto.ICondition) {
        return new ConditionObj().init(cond)
    }

    // 返回:收集道具类型的条件对象
    public conditionObjGetItem(target: TaskTarget[]): ConditionObj {
        let { type, id, num } = target[0]
        let data: Condition = { type: type || ConditionType.PROP, id, num }
        return new ConditionObj().init2(data)
    }

    public addStarDust(val: number, isEmit: boolean = true) {
        this.changeCurrency(ConditionType.STAR_DUST, val, isEmit)
    }

    public getStarDust() {
        return this.getCurrency(ConditionType.STAR_DUST)
    }

    public getHeart() {
        return this.getCurrency(ConditionType.HEART)
    }

    public getTicket() {
        return this.bag.getPropCountById(ItemID.TICKET)
    }

    public addHeart(val, isEmit: boolean = true) {
        this.changeCurrency(ConditionType.HEART, val, isEmit)
    }

    public addDiamond(val: number, isEmit: boolean = true) {
        this.changeCurrency(ConditionType.DIAMOND, val, isEmit)
    }

    public getDiamond() {
        return this.getCurrency(ConditionType.DIAMOND)
    }

    /**
     * 获取货币
     * @param type 货币类型
     * @returns
     */
    public getCurrency(type: ConditionType) {
        return this.currency.getCurrency(type)
    }

    public getClientCurrency(type: ConditionType) {
        return this.currency.getCurrency(type) + dropItemHelper.getClientCurreny(type) - this.currency.getDelayCurrency(type)
    }

    /**
     * 变更货币
     * @param type 货币类型
     * @param val
     * @param isEmit
     * @returns
     */
    public changeCurrency(type: ConditionType, val: number, isEmit: boolean = true): boolean {
        return this.currency.changeCurrency(type, val, isEmit);
    }

    public getEnough(type: ConditionType, target: number): boolean {
        return this.currency.getEnough(type, target)
    }

    private ChangeCostByCond(cond: ConditionObj, change: number = 1, isEmit: boolean = true) {
        let type = cond.type
        let id = cond.id
        let num = cond.num * change
        let currencyEmit = num > 0 ? false : isEmit
        if (type === ConditionType.STAR_DUST) {
            this.changeCurrency(ConditionType.STAR_DUST, num, currencyEmit)
        } else if (type == ConditionType.DIAMOND) {
            this.changeCurrency(ConditionType.DIAMOND, num, currencyEmit)
        } else if (type == ConditionType.HEART) {
            this.changeCurrency(ConditionType.HEART, num, currencyEmit)
        } else if (type == ConditionType.WORLD_TIME) {
            let time = num * ut.Time.Minute
            gameHelper.world.addTime(time)
            gameHelper.train.addOutputTime(gameHelper.world.toRealSecond(time))
        } else if (type === ConditionType.PROP) { //道具
            let c
            if (!cond.clone) {
                c = gameHelper.toCondition(cond)
            }
            else {
                c = cond.clone()
            }
            c.num = num
            this.bag.changePropByCond(c, isEmit)
        } else if (type == ConditionType.PASSENGER) {
            gameHelper.passenger.addPassenger(id as number)
        } else if (type == ConditionType.BUILD_ID) {//设施
            gameHelper.train.unlockBuildByReward(id.toString())
        } else if (type == ConditionType.ENTRUST_SEAT) {
            eventCenter.emit(EventType.ENTRUST_UNLOCK, num)
        } else if (type == ConditionType.CHEST) {
            let itemId = cfgHelper.getChestItemId(+cond.id)
            gameHelper.chest.changeChest(itemId, num)
        } else if (type == ConditionType.EQUIP) {
            if (change > 0) {
                gameHelper.equip.addEquip(Number(cond.id), cond.extra)
            } else {
                gameHelper.equip.delEquip(cond.extra.uid)
            }

        } else if (type == ConditionType.PASSENGER_SKIN) {
            gameHelper.passenger.unlockSkin(String(cond.id))
        } else if (type == ConditionType.SEED) {
            gameHelper.field.changeSeed(+cond.id, num)
        } else if (type == ConditionType.BLACK_HOLE_CURRENCY) {
            gameHelper.blackHole.changeCurrency(num)
        } else if (type == ConditionType.ORE_ITEM) {
            gameHelper.ore.changeOreItem(id as number, num)
        } else if (type == ConditionType.CHARACTER_FRAG) {
            gameHelper.passenger.changeFrag(+cond.id, cond.num)
        } else if (type == ConditionType.ARREST_CURRENCY) {
            gameHelper.arrest.changeCurrency(num)
        } else if (type == ConditionType.CHARACTER_PROFILE) {
            gameHelper.archives.addProfile(cond.id as number, cond.num)
        } else if (type == ConditionType.PLANET_PROFILE) {
            gameHelper.planetArchives.addProfile(cond.id as number, cond.num)
        } else if (type == ConditionType.AD) {
            gameHelper.ad.deductTimes(cond.id as proto.AdType)
        } else if (type == ConditionType.PROFILE_BRANCH_ENERGY) {
            gameHelper.profileBranch.changeEnergy(num)
        }
        else {
            twlog.error("grantReward fail:", cond)
        }
    }

    // 发放奖励
    public grantReward(cond: ConditionObj, isEmit: boolean = true) {
        this.ChangeCostByCond(cond, 1, isEmit)
    }

    public grantRewards(list: ConditionObj[], isEmit: boolean = true) {
        list && list.forEach(m => this.grantReward(m, isEmit))
    }

    // 发放奖励 并显示UI
    public async grantRewardAndShowUI(list: ConditionObj[] | ConditionObj, needMerge: boolean = true, options: {} & any = {}) {
        if (!list) return
        const arr = Array.isArray(list) ? list : [list]
        this.grantRewards(arr)
        await viewHelper.showGeneralReward(arr, needMerge, options)
    }

    public getConditionType(id: number | string) {
        return Number(String(id).substring(0, 2))
    }

    public checkCondition(cond: ConditionObj | Condition, out?) {
        let curNum = this.getNumByCondition(cond)
        let diff = curNum - cond.num
        if (out) {
            out.num = diff
        }
        return diff >= 0
    }

    public getNumByCondition(cond: ConditionObj | Condition) {
        let { type, id } = cond

        switch (type) {
            case ConditionType.STAR_DUST:
                return gameHelper.getCurrency(ConditionType.STAR_DUST)
            case ConditionType.HEART:
                return gameHelper.getCurrency(ConditionType.HEART)
            case ConditionType.DIAMOND:
                return gameHelper.getCurrency(ConditionType.DIAMOND)
            case ConditionType.PROP:
                return gameHelper.bag.getPropCountById(Number(id))
            case ConditionType.CHEST:
                return gameHelper.chest.getChestCountById(Number(id))
            case ConditionType.BLACK_HOLE_CURRENCY:
                return gameHelper.blackHole.getCurrency()
            case ConditionType.ORE_ITEM:
                return gameHelper.ore.getOreCountById(Number(id))
            case ConditionType.EQUIP:
                return gameHelper.equip.getEquipNum(+cond.id)
            case ConditionType.ARREST_CURRENCY:
                return gameHelper.arrest.getCurrency()
            case ConditionType.PROFILE_BRANCH_ENERGY:
                return gameHelper.profileBranch.getEnergy()
            case ConditionType.JACKPOT_COUNT:
                return gameHelper.jackpot.getJackpotTotalCount()
            case ConditionType.TASK_DIALOG:
                return 0
            default:
                twlog.error("getNumByCondition not found", cond.type)
                return 0
        }
    }

    public checkConditions(conds: (ConditionObj | Condition)[], failList?: any[]) {
        let succ = true
        for (let cond of conds) {
            let o
            if (failList) {
                o = new ConditionObj().init2(cond)
            }
            let res = this.checkCondition(cond, o)
            if (!res) {
                succ = false
                if (failList) {
                    failList.push(o)
                } else {
                    break
                }
            }
        }
        return succ
    }

    public getLevelByCond(cond: ConditionObj) {
        let id = cond.id
        let level = 1
        let curreny = CURRENY_CFG[cond.type]
        if (curreny) {
            level = curreny.level
        }
        else {
            let data
            if (cond.type == ConditionType.EQUIP) {
            }
            else if (cond.type == ConditionType.CHEST) {
                id = cfgHelper.getChestItemId(+id)
                data = assetsMgr.getJsonData<ItemCfg>("Item", id)
            }
            else if (cond.type == ConditionType.PROP) {
                data = assetsMgr.getJsonData<ItemCfg>("Item", id)
            }
            if (data) {
                level = data.level
            }
        }
        return level
    }

    public checkAryTypeIds(ary: TypeIds[]) {
        if (!ary) return true
        for (const { type, id } of ary) {
            if (!this.checkTypeIds(type, id)) {
                return false
            }
        }
        return true
    }

    public checkTypeIds(type: number, ids: any[]) {
        if (type == ConditionType.BUILD_ID) {
            for (const id of ids) {
                if (!gameHelper.train.isUnlockBuild(id)) {
                    return false
                }
            }
        } else {
            cc.error("checkTypeIds unknown type", type, ids)
        }
        return true
    }

    public getBuildNamesByAryTypeIds(ary: TypeIds[]) {
        let str = ''
        if (!ary) return str
        for (const { type, id } of ary) {
            if (type == ConditionType.BUILD_ID) {
                for (const key of id) {
                    let cfg = cfgHelper.getBuildById(key)
                    if (cfg) {
                        if (str.length > 0) str += assetsMgr.lang('common_guiText_21')
                        str += assetsMgr.lang(cfg.name)
                    }
                }
            }
        }
        return str
    }

    public showFailTips(failList: any[]) {
        if (failList.length == 0) return
        //viewHelper.showPnl('common/JumpTipPnl', this.toConditions(failList))
        let datas = assetsMgr.getJson<JumpTipsCfg>('JumpTips').datas
        let getCfg = (data: ConditionObj) => {
            return datas.find(cfg => data.isSame(cfg.cond)) || datas.find(cfg => data.type == cfg.cond.type)
        }
        failList = failList.map(c => {
            if (!(c instanceof ConditionObj)) {
                return new ConditionObj().init2(c)
            }
            return c
        })
        failList.sort((a, b) => {
            let cfgA = getCfg(a)
            let cfgB = getCfg(b)
            return cfgA.priority - cfgB.priority
        })
        let cfg = getCfg(failList[0])
        if (cfg) {
            viewHelper.showAlert(cfg.title)
        }
        else {
            viewHelper.showAlert("title_jumpTips_3")
        }
    }

    public deductCondition(cond: ConditionObj) {
        this.ChangeCostByCond(cond, -1)
    }

    public deductConditions(conds: ConditionObj[]) {
        return conds.forEach(cond => this.deductCondition(cond))
    }

    public mergeCondition(conds: ConditionObj[]) {
        if (!conds) return [] as ConditionObj[]
        let map = {}
        let res = []
        conds.forEach(m => {
            if (!!m.extra) {
                res.push(m)
                return
            }
            if (!map[m.type]) map[m.type] = {}
            if (!map[m.type][m.id]) {
                let cond = new ConditionObj().init(m.type, m.id, 0)
                map[m.type][m.id] = cond
                res.push(cond)
            }
            map[m.type][m.id].num += m.num
        })
        return this.mergeChestCondition(res)
    }

    //因为宝箱有两个id，一个是itemId,每个itemId对应一种宝箱一种外观；一个是宝箱唯一id。每种itemId可能对应多个id。所以写这个方法把不同id但是是同类型的宝箱合到一起
    public mergeChestCondition(conds: ConditionObj[]) {
        let map = {}
        let res = []
        conds.forEach(m => {
            if (m.type != ConditionType.CHEST) {
                res.push(m)
            }
            else {
                let id = cfgHelper.getChestItemId(+m.id)
                if (!map[id]) {
                    let cond = new ConditionObj().init(m.type, id, 0)
                    map[id] = cond
                    res.push(cond)
                }
                map[id].num += m.num
            }
        })
        return res
    }

    // 多语言倒计时，单位毫秒, 格式： x天x时x分, 小于1小时显示 mm:ss
    public updateCountDown(val: number, short: boolean = false) {
        let cfg = TIME_LANG
        if (short) {
            cfg = TIME_SHORT_LANG
        }
        if (val >= ut.Time.Day) {
            const str = val % ut.Time.Day >= ut.Time.Hour ? ut.millisecondFormat(val, 'd{0}h{1}') : ut.millisecondFormat(val, 'd{0}')
            return ut.stringFormat(str, [assetsMgr.lang(cfg.d), assetsMgr.lang(cfg.h)])
        } else if (val >= ut.Time.Hour) {
            const str = val % ut.Time.Hour >= ut.Time.Minute ? ut.millisecondFormat(val, 'h{0}m{1}') : ut.millisecondFormat(val, 'h{0}')
            return ut.stringFormat(str, [assetsMgr.lang(cfg.h), assetsMgr.lang(cfg.m)])
        } else {
            return ut.millisecondFormat(val, 'mm:ss')
        }
    }

    // 多语言倒计时, 格式： x天x时x分x秒
    public updateCountDown2(val: number, short: boolean = false) {
        let cfg = TIME_LANG
        if (short) {
            cfg = TIME_SHORT_LANG
        }
        if (val >= ut.Time.Day) {
            const str = val % ut.Time.Day >= ut.Time.Hour ? ut.millisecondFormat(val, 'd{0}h{1}') : ut.millisecondFormat(val, 'd{0}')
            return ut.stringFormat(str, [assetsMgr.lang(cfg.d), assetsMgr.lang(cfg.h)])
        } else if (val >= ut.Time.Hour) {
            const str = val % ut.Time.Hour >= ut.Time.Minute ? ut.millisecondFormat(val, 'h{0}m{1}') : ut.millisecondFormat(val, 'h{0}')
            return ut.stringFormat(str, [assetsMgr.lang(cfg.h), assetsMgr.lang(cfg.m)])
        } else if (val >= ut.Time.Minute) {
            const str = val % ut.Time.Minute >= ut.Time.Second ? ut.millisecondFormat(val, 'm{0}s{1}') : ut.millisecondFormat(val, 'm{0}')
            return ut.stringFormat(str, [assetsMgr.lang(cfg.m), assetsMgr.lang(cfg.s)])
        } else {
            const str = ut.millisecondFormat(val, 's{0}')
            return ut.stringFormat(str, [assetsMgr.lang(cfg.s)])
        }
    }

    public sortItemRewards(rewards: ConditionObj[]) {
        rewards.sort((a, b) => {
            let cfgA = assetsMgr.getJsonData<ItemCfg>("Item", a.id)
            let cfgB = assetsMgr.getJsonData<ItemCfg>("Item", b.id)
            if (cfgA.type == cfgB.type) {
                return cfgB.level - cfgA.level
            }
            return cfgA.type - cfgB.type
        })
        return rewards
    }

    public sortPassengers(roles: CharacterCfg[] | PassengerModel[]) {
        let isResonacing = gameHelper.passenger.isResonacing
        roles.sort((a: CharacterCfg | PassengerModel, b: CharacterCfg | PassengerModel) => {
            let rA = a instanceof PassengerModel ? a : gameHelper.passenger.getPassenger(a.id)
            let rB = b instanceof PassengerModel ? b : gameHelper.passenger.getPassenger(b.id)
            let hasA = Number(!!(rA))
            let hasB = Number(!!(rB))
            if (hasA != hasB) {
                return hasB - hasA
            }
            if (hasA && hasB) {
                let aRLV: number, bRLV: number
                if (isResonacing) {
                    let inA = gameHelper.passenger.isInResonace(a.id)
                    let inB = gameHelper.passenger.isInResonace(b.id)
                    if (inA != inB) {
                        return Number(inB) - Number(inA)
                    }
                    aRLV = inA ? rA.getLevel() : rA.getResonanceLv()
                    bRLV = inB ? rB.getLevel() : rB.getResonanceLv()
                } else {
                    aRLV = rA.getLevel(), bRLV = rB.getLevel()
                }
                if (aRLV != bRLV) {
                    return bRLV - aRLV
                }
                let aStar = rA.getStarLv(), bStar = rB.getStarLv()
                if (aStar != bStar) {
                    return bStar - aStar
                }
            }
            if (a.quality != b.quality) {
                return b.quality - a.quality;
            }
            return a.sortId - b.sortId
        })
    }

    public battlePassengersCmp(a: any, b: any) {
        let resonances = gameHelper.resonance.getResonanceRoles()
        let hasA = resonances.some(r => r.uid == b.uid)
        let hasB = resonances.some(r => r.uid == b.uid)
        if (hasA != hasB) {
            return Number(hasB) - Number(hasA)
        }
        if (a.getLevel() != b.getLevel()) {
            return b.getLevel() - a.getLevel();
        }
        if (a.getStarLv() != b.getStarLv()) {
            return b.getStarLv() - a.getStarLv();
        }
        return a.sortId - b.sortId
    }

    public isInland() {
        return version.getAppType() == "inland"
    }

    public isGLobal() {
        return !this.isInland()
    }

    //检测账号密码格式
    public checkAccountFormat(account, password) {
        if (!account) {
            viewHelper.showLoadAlert("login_guiText_15")
            return
        }
        else if (!password) {
            viewHelper.showLoadAlert("login_guiText_16")
            return
        }
        let reg = /^\w+$/ //只能数字、字母、下划线
        if (!reg.test(account)) {
            viewHelper.showLoadAlert("login_tips_5")
            return
        }
        else if (!reg.test(password)) {
            viewHelper.showLoadAlert("login_tips_6")
            return
        }
        return true
    }

    //调试内存
    public dumpMemory() {
        let nameMap = {}
        let assetsMap = cc.assetManager.assets['_map'], totalSize = 0

        for (let key in assetsMap) {
            const m = assetsMap[key]
            if (m instanceof cc.SpriteFrame) {
                let spr = m as cc.SpriteFrame
                let tex = spr.getTexture()
                if (tex && spr.name && spr.name !== '') {
                    nameMap[tex.nativeUrl] = spr.name
                }
            }
        }
        for (let key in assetsMap) {
            const m = assetsMap[key]
            if (m instanceof sp.SkeletonData) {
                let spine = m as sp.SkeletonData
                let texs = spine.textures
                texs.forEach(tex => {
                    if (spine.name && spine.name !== '') {
                        nameMap[tex.nativeUrl] = spine.name
                    }
                });
            }
        }
        const WHILTE_MAP = {
        }
        for (let key in assetsMap) {
            const m = assetsMap[key]

            if (m instanceof cc.Texture2D) {
                let tex = m as cc.Texture2D
                let size = tex.width * tex.height
                totalSize += size * 4

                const name = nameMap[tex.nativeUrl] || tex.nativeUrl
                if (!WHILTE_MAP[name]) {
                    if (size >= 1024 * 1024) {
                        console.error("[1024 TEX]: ", name, tex.width + 'x' + tex.height)
                    }
                    else if (size >= 800 * 800) {
                        console.warn("[800 TEX]: ", name, tex.width + 'x' + tex.height)
                    }
                    else if (size >= 512 * 512) {
                        console.log("[512 TEX]: ", name, tex.width + 'x' + tex.height)
                    }
                }
            }
        }
        let res = totalSize / 1024 / 1024
        res = Math.ceil(res * 100) / 100
        console.log('memory cost', res + 'm')
    }

    public getUid() { return gameHelper.user.getUid() }
    public getSid() { return gameHelper.user.getSid() }

    public checkPassengerById(id: number) {
        return this.getRoleType(id) == BattleRoleType.PASSENGER
    }

    // 拷贝id
    public async copyUid() {
        const text: any = this.user.getUid()
        return this.copyToClipboard(text, 'profile_tips_1')
    }

    // 拷贝到剪切板
    public async copyToClipboard(text: string, toast: string) {
        if (!text) {
        } else if (cc.sys.isBrowser) {
            if (navigator?.clipboard) {
                await navigator.clipboard.writeText(text)
            } else {
                return
            }
        } else if (ut.isMiniGame()) {
            await wxPro.setClipboardData({ data: text })
        } else if (ut.isMobile()) {
            await jsbHelper.call(JsbEvent.COPY_TO_CLIPBOARD, { data: text })
        } else {
            return
        }
        toast && viewHelper.showAlert(toast)
    }

    public isBoss(id: number) {
        let cfg = assetsMgr.checkJsonData<PlanetMonsterCfg>("PlanetMonster", id)
        return cfg?.isBoss
    }

    public isNpc(id: number) {
        return id > 4000
    }

    // 获取进入参数
    public getEnterQuery() {
        if (ut.isMiniGame()) {
            return wxHelper.getEnterInfo()
        }
        return jsbHelper.getAwakeParams()
    }

    public transCondToBuildAttr(cond: ConditionObj) {
        if (cond.type == ConditionType.STAR_DUST) {
            return BuildAttr.STAR
        }
        else if (cond.type == ConditionType.HEART) {
            return BuildAttr.HEART
        }
        else if (cond.type == ConditionType.PROP) {
            if (cond.id == ItemID.ELECTRIC) {
                return BuildAttr.ELECTRICITY
            }
            else if (cond.id == ItemID.WATER) {
                return BuildAttr.WATER
            }
            else if (cond.id == ItemID.VITALITY) {
                return BuildAttr.VITALITY
            }
        }
    }

    public getRoleType(id: number) {
        let type = BattleRoleType.SUMMON
        if (assetsMgr.checkJsonData("Character", id)) {
            type = BattleRoleType.PASSENGER
        }
        else if (assetsMgr.checkJsonData("PlanetMonster", id)) {
            type = BattleRoleType.MONSTER
        }
        return type
    }

    public getAppShopUrl() {
        if (ut.isAndroid()) {
            if (this.isInland()) {
            }
            return "https://play.google.com/store/apps/details?id=global.train.twgame"
        }
        else if (ut.isIos()) {
            if (this.isInland()) {
            }
            return ""
        }
        else {
            return ""
        }
    }

    public toBattleRole(e: proto.IBattleRole, isMonster = true) {
        let data: any = { uid: e.uid, id: e.id, level: e.lv, starLv: e.starLv, talents: e.talents, equips: e.equips, attrRate: e.attrRate }
        if (isMonster) {
            data.tmpId = 4
        }
        let r = new PassengerModel().initData(data)
        let skills = r.getSkills()
        return new BattleRole().initData({ uid: e.uid, id: e.id, hp: r.getHp(), attack: r.getAttack(), skills, lv: r.getLevel(), starLv: r.getStarLv(), role: r })
    }

    public colorfulSkill(content: string, params: { boldColor?: string, gx?: boolean } = {}) {
        let boldColor = params.boldColor
        if (boldColor) {
            content = content.replace(new RegExp('<b>', 'g'), `<color=${boldColor}>`)
        }
        if (params.gx !== false) {
            content = content.replace(new RegExp('<x>', 'g'), "<color=#ff5276>")
            content = content.replace(new RegExp('<g>', 'g'), "<color=#31a8f8>")
            content = content.replace(new RegExp('<a>', 'g'), "<color=#f09615>")
        }
        return content
    }

    public isRoleTicket(cond) {
        let modelCfg = cfgHelper.getCharacter(+cond.id)
        if (modelCfg && cond.type == ConditionType.PROP) {
            return true
        }
    }

    public getTimeStoneKeyIdByPage(pageIndex: number) {
        return pageIndex + 400 - 1
    }

    public getNotebookPageById(id: number) {
        return id - 400 + 1
    }

    public isAgreement() {
        return storageMgr.getOrgItem("__@agreement") == '1'
    }

    public getPlanetMoveTime(from: number, to: number) {
        let time = cfgHelper.getPlanetMoveTime(from, to).time
        // 运送时  时长翻倍
        if (this.transport.isTransporting()) {
            const timeRate = cfgHelper.getMiscData("transport").timeRate || 1
            time *= timeRate
        }
        time = Math.round(time * (1 - gameHelper.trainTech.getTrainSpeed()))
        return time
    }

    public getTrainBurstTask() {
        // let roles = [1002, 1006, 1007]
        // return [{
        //     id: TrainBurstTaskType.FIRE,
        //     roles,
        //     trainId: 1013,
        // }]
        return []
    }

}

export const gameHelper = new GameHelper()
if (CC_DEV) {
    window['gameHelper'] = gameHelper
}
