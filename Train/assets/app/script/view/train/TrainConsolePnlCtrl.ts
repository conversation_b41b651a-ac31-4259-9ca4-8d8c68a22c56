import { UIFunctionType } from "../../common/constant/Enums";
import { resHelper } from "../../common/helper/ResHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import TrainBuildWdtCtrl from "./TrainBuildWdtCtrl";
import TrainTechWdtCtrl from "./TrainTechWdtCtrl";

const { ccclass } = cc._decorator;

enum TabType {
    Tech,
    Build,
}

type TabArg = {
    title: string
    path: string
    lock: boolean
    ctrl: any
    functionType?: UIFunctionType
}

const TAB_ARG: { [key in TabType]: TabArg } = {
    [TabType.Tech]: {
        title: "trainTech_guiText_0",
        path: "train/TrainTechWdt",
        lock: false,
        ctrl: TrainTechWdtCtrl,
        functionType: UIFunctionType.TRAIN_TECH,
    },
    [TabType.Build]: {
        title: "trainBuild_guiText_17",
        path: "train/TrainBuildWdt",
        lock: false,
        ctrl: TrainBuildWdtCtrl,
    },
}

@ccclass
export default class TrainConsolePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected titleLbl_: cc.Label = null // path://root/lcjs_zjm_title_01/title_l
    protected wdtBodyNode_: cc.Node = null // path://root/wdt_body_n
    protected selectTabNode_: cc.Node = null // path://root/selectTab_n
    protected tabsNode_: cc.Node = null // path://root/tabs_nbe_n
    protected backNode_: cc.Node = null // path://back_be_n
    //@end
    private _selectedTabType: TabType = TabType.Build

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        for (const tabType in TAB_ARG) {
            const arg = TAB_ARG[tabType]
            // 这里设置tab lock
            let functionType = arg.functionType
            arg.lock = functionType && !unlockHelper.isUnlockFunction(functionType)
        }
        this.initView()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/tabs_nbe_n
    onClickTabs(event: cc.Event.EventTouch, data: string) {
        let it: cc.Node = event.target
        let type = Number(it.name)
        if (this._selectedTabType == type) return
        const ext = it.Data as TabArg
        if (ext.lock) return void viewHelper.showAlert("common_buildType_unlock")
        this._selectedTabType = type
        this.updateTabs()
        this.handleTab()
    }

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------
    private initView() {
        this.initTabs()
        this.updateTabs()
        this.handleTab()
    }

    initTabs() {
        this.tabsNode_.Component(cc.Layout).enabled = false
        const tabArray = [
            { type: TabType.Tech, selected: false, lock: !!TAB_ARG[TabType.Tech].lock },
            { type: TabType.Build, selected: false, lock: !!TAB_ARG[TabType.Build].lock },
        ]
        tabArray[this._selectedTabType].selected = true
        this.tabsNode_.children.forEach(it => {
            let type = Number(it.name)
            let data = tabArray.find(m => m.type == type)
            it.Data = data
            if (data.lock) {
                it.Component(cc.MultiFrame).setFrame(2)
                return
            }
            it.Component(cc.MultiFrame).setFrame(data.selected ? 1 : 0)
        })
    }

    updateTabs() {
        let preNode = this.selectTabNode_.children[0]
        if (preNode) {
            preNode.parent = this.tabsNode_
            preNode.Component(cc.MultiFrame).setFrame(0)
        }
        let node = this.tabsNode_.FindChild(String(this._selectedTabType))
        node.parent = this.selectTabNode_
        node.Component(cc.MultiFrame).setFrame(1)
    }

    async handleTab() {
        const it = this.wdtBodyNode_.Swih(this._selectedTabType)[0]
        const arg = TAB_ARG[this._selectedTabType]
        this.titleLbl_.setLocaleKey(arg.title)
        if (!arg.ctrl) {
            return
        }
        resHelper.addPrefabCmptLock(arg.ctrl as any, arg.path, it, this.getTag()).then((m: any) => {
            m.init()
        })
    }

}
