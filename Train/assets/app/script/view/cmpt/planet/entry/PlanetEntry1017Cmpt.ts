import { Hero<PERSON>ni<PERSON>, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetEntry1017Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
        ]
    }

    private showPnls: string[] = []


    protected initView() {
        this.initPvp()
    }

    private initPvp() {
        let type = UIFunctionType.PLAY_PVP_1
        let pvp = this.Component(MountPointCmpt).getPoint("pvp")
        pvp.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        pvp.on("click", () => {
            viewHelper.showPnl("pvp/PvpSelectPnl")
        })
    }


    private onFunctionUnlock(type: UIFunctionType) {
    }
}