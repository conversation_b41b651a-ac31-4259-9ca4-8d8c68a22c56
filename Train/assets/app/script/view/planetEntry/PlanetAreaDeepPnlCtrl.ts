import { RuleType } from "../../common/constant/Enums";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { timeHelper } from "../../common/helper/TimeHelper";
import PlanetModel from "../../model/planet/PlanetModel";
import EventType from "../../common/event/EventType";
import EventCenter from "../../../core/utils/EventCenter";
import { cfgHelper } from "../../common/helper/CfgHelper";

const { ccclass } = cc._decorator;
const eventCenter = new EventCenter();

@ccclass
export default class PlanetAreaDeepPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected maskNode_: cc.Node = null // path://mask_n
    protected bgNode_: cc.Node = null // path://bg_n
    protected iconNode_: cc.Node = null // path://left/icon_n
    protected tableNode_: cc.Node = null // path://left/table_n
    protected posCfgNode_: cc.Node = null // path://left/posCfg_n
    protected contentNode_: cc.Node = null // path://left/content_n
    protected nameLbl_: cc.Label = null // path://right/name/name_l
    protected proSpr_: cc.Sprite = null // path://right/pNode/cfts_jdt_1/pro_s
    protected proLbl_: cc.Label = null // path://right/pNode/pro_l
    protected playsNode_: cc.Node = null // path://right/plays_n
    protected shipLbl_: cc.Label = null // path://right/cfts_icon_bak1/ship_l
    //@end

    private index: number = 0
    private model: PlanetModel = null

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false })
        this.node.opacity = 0
    }

    public listenEventMaps() {
        return [
            { [EventType.PLANET_DEEP_EXPLORE_START]: this.onExploreStart },
            { [EventType.PLANET_DEEP_EXPLORE_SHIP_CHANGED]: this.setShip },
        ]
    }

    public onEnter(data: any) {
        this.model = gameHelper.planet.getCurPlanet()
        this.index = this.model.getId() != 1014 ? 0 : 1
        this.initView()

        // 淡入动画
        cc.tween(this.node)
            .to(0.15, { opacity: 255 })
            .start()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://title/rule_be
    onClickRule(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('battle/EquipLvRules', RuleType.DEEP_EXPLORE)
    }

    // path://back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://right/cfts_icon_bak1/add_be
    onClickAdd(event: cc.Event.EventTouch, data: string) {
    }

    // path://right/exp_be
    onClickExp(event: cc.Event.EventTouch, data: string) {
        if (!gameHelper.deepExplore.hasFreeShip()) {
            return void viewHelper.showAlert("ui_deepExplore_tips_4")
        }
        viewHelper.showPnl("planetEntry/DeepReadyPnl")
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.initBg()
        this.initIcon()
        this.initMain()
        this.initInfo()
    }

    private initBg() {
        let index = this.index
        this.bgNode_.Swih(index)
        this.maskNode_.Component(cc.MultiColor).setColor(index)
    }

    private initIcon() {
        let index = this.index
        this.tableNode_.Component(sp.Skeleton).setSkin(index ? "skin2" : "skin1")
        resHelper.loadPlanetEntryIcon(this.model.getId(), this.iconNode_.Component(cc.Sprite), this.getTag())
    }

    private initMain() {
        let planet = gameHelper.planet.getCurPlanet()
        let areas = planet.getAreas()
        let curArea = planet.getCurArea()
        let index = this.index

        for (let child of this.posCfgNode_.children) {
            child.active = false
        }

        this.contentNode_.Items(areas, (it, area, i) => {
            let cfgNode = this.posCfgNode_.children[i]
            if (!cfgNode) {
                twlog.error("cfgNode is null")
                return
            }
            cfgNode.active = true
            let lineIndex = area == curArea ? index : index + 2
            cfgNode.Child("line1").Component(cc.MultiFrame).setFrame(lineIndex)
            cfgNode.Child("line2").Component(cc.MultiFrame).setFrame(lineIndex)

            let pos = cfgNode.getPosition()
            it.setPosition(pos)

            let nameNode
            it.anchorX = cfgNode.anchorX
            let node
            if (area.isDone()) {
                node = it.Swih("done")[0]
                node.Child("icon", cc.MultiFrame).setFrame(index)
                nameNode = node.Child("name")
                nameNode.Component(cc.MultiColor).setColor(index + 2)
                nameNode.Component(cc.MultiColor).setOutlineColor(index)
                node.Component(cc.Layout).horizontalDirection = it.anchorX ? cc.Layout.HorizontalDirection.LEFT_TO_RIGHT : cc.Layout.HorizontalDirection.RIGHT_TO_LEFT
            } else if (area == curArea) {
                node = it.Swih("unlock")[0]

                let bgNode = node.Child("bg")
                bgNode.Component(cc.MultiFrame).setFrame(index)
                bgNode.off("click")
                bgNode.on("click", () => {
                    viewHelper.gotoPlanetMain()
                })
                bgNode.scaleX = it.anchorX ? -1 : 1
                bgNode.anchorX = 0

                nameNode = bgNode.Child("name")
                nameNode.scaleX = bgNode.scaleX
                nameNode.anchorX = it.anchorX
                nameNode.Component(cc.MultiColor).setOutlineColor(index)

                let progressLb = node.Child("progressLb")
                progressLb.Component(cc.MultiColor).setOutlineColor(index)
                progressLb.Component(cc.Label).string = `${curArea.index}-${curArea.getProgress() + 1}`

                let progressBg = node.Child("progressBg")
                progressBg.Component(cc.MultiFrame).setFrame(index)
                let progress = progressBg.Child("mask/progress")
                progress.x = cc.misc.lerp(-progress.width, 0, area.getPercent())

                for (let child of node.children) {
                    child.x = it.anchorX ? -Math.abs(child.x) : Math.abs(child.x)
                }
            } else {
                node = it.Swih("lock")[0]

                let bgNode = node.Child("bg")
                bgNode.Component(cc.MultiFrame).setFrame(index)
                bgNode.off("click")
                bgNode.on("click", () => {
                    viewHelper.showAlert("planetEntry_tips_1")
                })
                bgNode.scaleX = it.anchorX ? -1 : 1
                bgNode.anchorX = 0

                nameNode = bgNode.Child("name")
                nameNode.scaleX = bgNode.scaleX
                nameNode.anchorX = it.anchorX
                nameNode.Component(cc.MultiColor).setColor(index)

                node.Child("suo", cc.MultiFrame).setFrame(index)

                for (let child of node.children) {
                    child.x = it.anchorX ? -Math.abs(child.x) : Math.abs(child.x)
                }
            }
            nameNode.setLocaleKey(area.name)
            node.anchorX = it.anchorX
        })
    }

    private initInfo() {
        let planet = gameHelper.planet.getCurPlanet()
        this.nameLbl_.setLocaleKey(planet.name)

        let playsKey = [
            "ui_area_deep_need_1", "ui_area_deep_need_2"
        ]

        let node = this.playsNode_.Swih("plays")[0]
        node.Items(playsKey, (it, data, index) => {
            if (index == 0) {
                it.Child("lb").setLocaleKey(data, 5)
            } else {
                it.Child("lb").setLocaleKey(data, timeHelper.getTimeText(planet.json.explore.time * 3600))
            }
        })

        const { cur, total } = gameHelper.planetArchives.getProgressByPlanetId(this.model.getId())
        this.proLbl_.string = Math.floor((cur / total) * 100) + "%"
        this.proSpr_.fillRange = cur / total

        this.setShip()
    }

    private onExploreStart() {
        this.close()
    }

    setShip() {
        this.shipLbl_.string = `${gameHelper.deepExplore.maxTeamCnt - gameHelper.deepExplore.usedTeamCnt}/${gameHelper.deepExplore.maxTeamCnt}`
    }

}
